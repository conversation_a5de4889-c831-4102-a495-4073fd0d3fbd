import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './services/auth.service';
import { JwtTokenService } from './services/jwt-token.service';
import { IsamService } from './services/isam.service';
import { StateIdParserService } from './services/stateIdParserService';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ApiAdapterClient, I18nLookupService, JwtAuthModule, LookupEntity, LookupsModule, RedisModule } from '@modules';
import { CaptchaService } from './services/captcha.service';
import { ForgetPasswordService } from './services/forget-password.service';
import { UserDetailsService } from './services/user-details.service';

@Module({
  imports: [
    HttpModule,
    ConfigModule,
    ApiAdapterClient,
    JwtAuthModule.forRoot(),
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
    ]),
    RedisModule.forRootAsync(),
  ],
  controllers: [AuthController],
  providers: [
    Logger,
    AuthService,
    JwtTokenService,
    IsamService,
    StateIdParserService,
    I18nLookupService,
    CaptchaService,
    ForgetPasswordService,
    UserDetailsService,
  ],
  exports: [AuthService, UserDetailsService],
})
export class AuthModule {
}
